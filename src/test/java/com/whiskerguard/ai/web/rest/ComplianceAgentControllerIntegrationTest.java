/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：ComplianceAgentControllerIntegrationTest.java
 * 包    名：com.whiskerguard.ai.web.rest
 * 描    述：ComplianceAgentController集成测试
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.web.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.WhiskerguardAiServiceApp;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.TaskPriority;
import com.whiskerguard.ai.service.agent.dto.AgentTaskRequestDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.*;

/**
 * ComplianceAgentController集成测试
 * <p>
 * 测试ComplianceAgentController的三个核心方法：
 * 1. createTask - 创建Agent任务
 * 2. getTaskStatus - 获取任务状态
 * 3. getTaskResult - 获取任务结果
 *
 * 重点测试任务的完整执行流程，确保业务逻辑正确执行。
 *
 * <AUTHOR>
 * @since 1.0
 */
@SpringBootTest(classes = WhiskerguardAiServiceApp.class)
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class ComplianceAgentControllerIntegrationTest {

    @Autowired
    private WebApplicationContext webApplicationContext;

    @Autowired
    private ObjectMapper objectMapper;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.webAppContextSetup(webApplicationContext).build();
    }

    /**
     * 测试创建外规内化任务的完整流程
     */
    @Test
    void testCreateRegulationInternalizationTask() throws Exception {
        // 1. 准备测试数据
        AgentTaskRequestDTO request = AgentTaskRequestDTO.builder()
                .tenantId(1L)
                .taskType(AgentTaskType.REGULATION_INTERNALIZATION.name())
                .title("电力行业安全生产法规内化测试")
                .description("测试将国家电力安全生产相关法规转化为公司内部管理制度")
                .priority(TaskPriority.NORMAL.name())
                .requestData("{\"regulationId\":\"REG_2024_001\",\"companyId\":1,\"industryType\":\"电力\",\"companyScale\":\"大型企业\",\"tenantId\":1,\"employeeId\":1001}")
                .build();

        // 2. 调用创建任务接口
        MvcResult createResult = mockMvc.perform(post("/api/compliance-agent/tasks")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.taskId").exists())
                .andExpect(jsonPath("$.taskType").value("REGULATION_INTERNALIZATION"))
                .andExpect(jsonPath("$.title").value("电力行业安全生产法规内化测试"))
                .andExpect(jsonPath("$.status").value("PENDING"))
                .andReturn();

        // 3. 解析响应获取任务ID
        String responseContent = createResult.getResponse().getContentAsString();
        System.out.println("创建任务响应: " + responseContent);

        // 从响应中提取taskId
        Long taskId = objectMapper.readTree(responseContent).get("taskId").asLong();
        System.out.println("创建的任务ID: " + taskId);

        // 4. 等待任务开始执行（模拟异步执行）
        Thread.sleep(2000);

        // 5. 查询任务状态
        mockMvc.perform(get("/api/compliance-agent/tasks/{taskId}/status", taskId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId").value(taskId))
                .andExpect(jsonPath("$.status").exists())
                .andExpect(jsonPath("$.progress").exists());

        // 6. 等待任务完成（根据实际情况调整等待时间）
        Thread.sleep(10000);

        // 7. 查询任务结果
        MvcResult resultResponse = mockMvc.perform(get("/api/compliance-agent/tasks/{taskId}/result", taskId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId").value(taskId))
                .andExpect(jsonPath("$.taskType").value("REGULATION_INTERNALIZATION"))
                .andExpect(jsonPath("$.status").exists())
                .andReturn();

        String resultContent = resultResponse.getResponse().getContentAsString();
        System.out.println("任务执行结果: " + resultContent);
    }

    /**
     * 测试创建制度审查任务
     */
    @Test
    void testCreatePolicyReviewTask() throws Exception {
        // 1. 准备测试数据
        AgentTaskRequestDTO request = AgentTaskRequestDTO.builder()
                .tenantId(1L)
                .taskType(AgentTaskType.POLICY_REVIEW.name())
                .title("企业安全管理制度审查测试")
                .description("测试审查企业内部安全管理制度的合规性")
                .priority(TaskPriority.HIGH.name())
                .requestData("{\"policyId\":\"POL_2024_001\",\"policyType\":\"安全管理\",\"industryType\":\"电力\",\"tenantId\":1,\"employeeId\":1001}")
                .build();

        // 2. 调用创建任务接口
        MvcResult createResult = mockMvc.perform(post("/api/compliance-agent/tasks")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.taskType").value("POLICY_REVIEW"))
                .andReturn();

        // 3. 获取任务ID并查询状态
        String responseContent = createResult.getResponse().getContentAsString();
        Long taskId = objectMapper.readTree(responseContent).get("taskId").asLong();

        Thread.sleep(2000);

        mockMvc.perform(get("/api/compliance-agent/tasks/{taskId}/status", taskId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId").value(taskId));
    }

    /**
     * 测试创建合同审查任务
     */
    @Test
    void testCreateContractReviewTask() throws Exception {
        // 1. 准备测试数据
        AgentTaskRequestDTO request = AgentTaskRequestDTO.builder()
                .tenantId(1L)
                .taskType(AgentTaskType.CONTRACT_REVIEW.name())
                .title("供电合同审查测试")
                .description("测试审查供电合同的合规性和风险")
                .priority(TaskPriority.URGENT.name())
                .requestData("{\"contractId\":\"CON_2024_001\",\"contractType\":\"供电合同\",\"tenantId\":1,\"employeeId\":1001}")
                .build();

        // 2. 调用创建任务接口
        MvcResult createResult = mockMvc.perform(post("/api/compliance-agent/tasks")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andDo(print())
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.taskType").value("CONTRACT_REVIEW"))
                .andReturn();

        // 3. 获取任务ID并查询状态
        String responseContent = createResult.getResponse().getContentAsString();
        Long taskId = objectMapper.readTree(responseContent).get("taskId").asLong();

        Thread.sleep(2000);

        mockMvc.perform(get("/api/compliance-agent/tasks/{taskId}/status", taskId))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId").value(taskId));
    }

    /**
     * 测试健康检查接口
     */
    @Test
    void testHealthCheck() throws Exception {
        mockMvc.perform(get("/api/compliance-agent/health"))
                .andDo(print())
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("UP"))
                .andExpect(jsonPath("$.service").value("compliance-agent"))
                .andExpect(jsonPath("$.timestamp").exists());
    }

    /**
     * 测试参数验证
     */
    @Test
    void testValidationErrors() throws Exception {
        // 测试空的请求体
        AgentTaskRequestDTO invalidRequest = AgentTaskRequestDTO.builder()
                .build();

        mockMvc.perform(post("/api/compliance-agent/tasks")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(invalidRequest)))
                .andDo(print())
                .andExpect(status().isBadRequest());
    }

    /**
     * 测试不存在的任务查询
     */
    @Test
    void testGetNonExistentTask() throws Exception {
        Long nonExistentTaskId = 99999L;

        mockMvc.perform(get("/api/compliance-agent/tasks/{taskId}/status", nonExistentTaskId))
                .andDo(print())
                .andExpect(status().isNotFound());

        mockMvc.perform(get("/api/compliance-agent/tasks/{taskId}/result", nonExistentTaskId))
                .andDo(print())
                .andExpect(status().isNotFound());
    }
}
