package com.whiskerguard.ai.web.rest;

import static com.whiskerguard.ai.domain.TaskStepAsserts.*;
import static com.whiskerguard.ai.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.IntegrationTest;
import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.domain.enumeration.TaskStepStatus;
import com.whiskerguard.ai.repository.TaskStepRepository;
import com.whiskerguard.ai.service.TaskStepService;
import com.whiskerguard.ai.service.dto.TaskStepDTO;
import com.whiskerguard.ai.service.mapper.TaskStepMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link TaskStepResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class TaskStepResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_STEP_NAME = "AAAAAAAAAA";
    private static final String UPDATED_STEP_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_STEP_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_STEP_DESCRIPTION = "BBBBBBBBBB";

    private static final TaskStepStatus DEFAULT_STATUS = TaskStepStatus.PENDING;
    private static final TaskStepStatus UPDATED_STATUS = TaskStepStatus.RUNNING;

    private static final Integer DEFAULT_STEP_ORDER = 1;
    private static final Integer UPDATED_STEP_ORDER = 2;

    private static final String DEFAULT_INPUT_DATA = "AAAAAAAAAA";
    private static final String UPDATED_INPUT_DATA = "BBBBBBBBBB";

    private static final String DEFAULT_OUTPUT_DATA = "AAAAAAAAAA";
    private static final String UPDATED_OUTPUT_DATA = "BBBBBBBBBB";

    private static final String DEFAULT_ERROR_MESSAGE = "AAAAAAAAAA";
    private static final String UPDATED_ERROR_MESSAGE = "BBBBBBBBBB";

    private static final Instant DEFAULT_START_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_START_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_END_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_END_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Long DEFAULT_EXECUTION_TIME = 1L;
    private static final Long UPDATED_EXECUTION_TIME = 2L;

    private static final Integer DEFAULT_RETRY_COUNT = 0;
    private static final Integer UPDATED_RETRY_COUNT = 1;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/task-steps";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private TaskStepRepository taskStepRepository;

    @Mock
    private TaskStepRepository taskStepRepositoryMock;

    @Autowired
    private TaskStepMapper taskStepMapper;

    @Mock
    private TaskStepService taskStepServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restTaskStepMockMvc;

    private TaskStep taskStep;

    private TaskStep insertedTaskStep;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TaskStep createEntity(EntityManager em) {
        TaskStep taskStep = new TaskStep()
            .tenantId(DEFAULT_TENANT_ID)
            .stepName(DEFAULT_STEP_NAME)
            .stepDescription(DEFAULT_STEP_DESCRIPTION)
            .status(DEFAULT_STATUS.getValue())
            .stepOrder(DEFAULT_STEP_ORDER)
            .inputData(DEFAULT_INPUT_DATA)
            .outputData(DEFAULT_OUTPUT_DATA)
            .errorMessage(DEFAULT_ERROR_MESSAGE)
            .startTime(DEFAULT_START_TIME)
            .endTime(DEFAULT_END_TIME)
            .executionTime(DEFAULT_EXECUTION_TIME)
            .retryCount(DEFAULT_RETRY_COUNT)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
        // Add required entity
        AgentTask agentTask;
        if (TestUtil.findAll(em, AgentTask.class).isEmpty()) {
            agentTask = AgentTaskResourceIT.createEntity();
            em.persist(agentTask);
            em.flush();
        } else {
            agentTask = TestUtil.findAll(em, AgentTask.class).get(0);
        }
        taskStep.setAgentTask(agentTask);
        return taskStep;
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TaskStep createUpdatedEntity(EntityManager em) {
        TaskStep updatedTaskStep = new TaskStep()
            .tenantId(UPDATED_TENANT_ID)
            .stepName(UPDATED_STEP_NAME)
            .stepDescription(UPDATED_STEP_DESCRIPTION)
            .status(UPDATED_STATUS.getValue())
            .stepOrder(UPDATED_STEP_ORDER)
            .inputData(UPDATED_INPUT_DATA)
            .outputData(UPDATED_OUTPUT_DATA)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .startTime(UPDATED_START_TIME)
            .endTime(UPDATED_END_TIME)
            .executionTime(UPDATED_EXECUTION_TIME)
            .retryCount(UPDATED_RETRY_COUNT)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        // Add required entity
        AgentTask agentTask;
        if (TestUtil.findAll(em, AgentTask.class).isEmpty()) {
            agentTask = AgentTaskResourceIT.createUpdatedEntity();
            em.persist(agentTask);
            em.flush();
        } else {
            agentTask = TestUtil.findAll(em, AgentTask.class).get(0);
        }
        updatedTaskStep.setAgentTask(agentTask);
        return updatedTaskStep;
    }

    @BeforeEach
    void initTest() {
        taskStep = createEntity(em);
    }

    @AfterEach
    void cleanup() {
        if (insertedTaskStep != null) {
            taskStepRepository.delete(insertedTaskStep);
            insertedTaskStep = null;
        }
    }

    @Test
    @Transactional
    void createTaskStep() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the TaskStep
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);
        var returnedTaskStepDTO = om.readValue(
            restTaskStepMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(taskStepDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            TaskStepDTO.class
        );

        // Validate the TaskStep in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedTaskStep = taskStepMapper.toEntity(returnedTaskStepDTO);
        assertTaskStepUpdatableFieldsEquals(returnedTaskStep, getPersistedTaskStep(returnedTaskStep));

        insertedTaskStep = returnedTaskStep;
    }

    @Test
    @Transactional
    void createTaskStepWithExistingId() throws Exception {
        // Create the TaskStep with an existing ID
        taskStep.setId(1L);
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restTaskStepMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(taskStepDTO)))
            .andExpect(status().isBadRequest());

        // Validate the TaskStep in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        taskStep.setTenantId(null);

        // Create the TaskStep, which fails.
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        restTaskStepMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(taskStepDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStepNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        taskStep.setStepName(null);

        // Create the TaskStep, which fails.
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        restTaskStepMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(taskStepDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        taskStep.setStatus(null);

        // Create the TaskStep, which fails.
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        restTaskStepMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(taskStepDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStepOrderIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        taskStep.setStepOrder(null);

        // Create the TaskStep, which fails.
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        restTaskStepMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(taskStepDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        taskStep.setVersion(null);

        // Create the TaskStep, which fails.
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        restTaskStepMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(taskStepDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        taskStep.setCreatedAt(null);

        // Create the TaskStep, which fails.
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        restTaskStepMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(taskStepDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        taskStep.setUpdatedAt(null);

        // Create the TaskStep, which fails.
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        restTaskStepMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(taskStepDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        taskStep.setIsDeleted(null);

        // Create the TaskStep, which fails.
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        restTaskStepMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(taskStepDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllTaskSteps() throws Exception {
        // Initialize the database
        insertedTaskStep = taskStepRepository.saveAndFlush(taskStep);

        // Get all the taskStepList
        restTaskStepMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(taskStep.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].stepName").value(hasItem(DEFAULT_STEP_NAME)))
            .andExpect(jsonPath("$.[*].stepDescription").value(hasItem(DEFAULT_STEP_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].stepOrder").value(hasItem(DEFAULT_STEP_ORDER)))
            .andExpect(jsonPath("$.[*].inputData").value(hasItem(DEFAULT_INPUT_DATA)))
            .andExpect(jsonPath("$.[*].outputData").value(hasItem(DEFAULT_OUTPUT_DATA)))
            .andExpect(jsonPath("$.[*].errorMessage").value(hasItem(DEFAULT_ERROR_MESSAGE)))
            .andExpect(jsonPath("$.[*].startTime").value(hasItem(DEFAULT_START_TIME.toString())))
            .andExpect(jsonPath("$.[*].endTime").value(hasItem(DEFAULT_END_TIME.toString())))
            .andExpect(jsonPath("$.[*].executionTime").value(hasItem(DEFAULT_EXECUTION_TIME.intValue())))
            .andExpect(jsonPath("$.[*].retryCount").value(hasItem(DEFAULT_RETRY_COUNT)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @SuppressWarnings({ "unchecked" })
    void getAllTaskStepsWithEagerRelationshipsIsEnabled() throws Exception {
        when(taskStepServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restTaskStepMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(taskStepServiceMock, times(1)).findAllWithEagerRelationships(any());
    }

    @SuppressWarnings({ "unchecked" })
    void getAllTaskStepsWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(taskStepServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restTaskStepMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(taskStepRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getTaskStep() throws Exception {
        // Initialize the database
        insertedTaskStep = taskStepRepository.saveAndFlush(taskStep);

        // Get the taskStep
        restTaskStepMockMvc
            .perform(get(ENTITY_API_URL_ID, taskStep.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(taskStep.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.stepName").value(DEFAULT_STEP_NAME))
            .andExpect(jsonPath("$.stepDescription").value(DEFAULT_STEP_DESCRIPTION))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.stepOrder").value(DEFAULT_STEP_ORDER))
            .andExpect(jsonPath("$.inputData").value(DEFAULT_INPUT_DATA))
            .andExpect(jsonPath("$.outputData").value(DEFAULT_OUTPUT_DATA))
            .andExpect(jsonPath("$.errorMessage").value(DEFAULT_ERROR_MESSAGE))
            .andExpect(jsonPath("$.startTime").value(DEFAULT_START_TIME.toString()))
            .andExpect(jsonPath("$.endTime").value(DEFAULT_END_TIME.toString()))
            .andExpect(jsonPath("$.executionTime").value(DEFAULT_EXECUTION_TIME.intValue()))
            .andExpect(jsonPath("$.retryCount").value(DEFAULT_RETRY_COUNT))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingTaskStep() throws Exception {
        // Get the taskStep
        restTaskStepMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingTaskStep() throws Exception {
        // Initialize the database
        insertedTaskStep = taskStepRepository.saveAndFlush(taskStep);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the taskStep
        TaskStep updatedTaskStep = taskStepRepository.findById(taskStep.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedTaskStep are not directly saved in db
        em.detach(updatedTaskStep);
        updatedTaskStep
            .tenantId(UPDATED_TENANT_ID)
            .stepName(UPDATED_STEP_NAME)
            .stepDescription(UPDATED_STEP_DESCRIPTION)
            .status(UPDATED_STATUS.getValue())
            .stepOrder(UPDATED_STEP_ORDER)
            .inputData(UPDATED_INPUT_DATA)
            .outputData(UPDATED_OUTPUT_DATA)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .startTime(UPDATED_START_TIME)
            .endTime(UPDATED_END_TIME)
            .executionTime(UPDATED_EXECUTION_TIME)
            .retryCount(UPDATED_RETRY_COUNT)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(updatedTaskStep);

        restTaskStepMockMvc
            .perform(
                put(ENTITY_API_URL_ID, taskStepDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(taskStepDTO))
            )
            .andExpect(status().isOk());

        // Validate the TaskStep in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedTaskStepToMatchAllProperties(updatedTaskStep);
    }

    @Test
    @Transactional
    void putNonExistingTaskStep() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        taskStep.setId(longCount.incrementAndGet());

        // Create the TaskStep
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTaskStepMockMvc
            .perform(
                put(ENTITY_API_URL_ID, taskStepDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(taskStepDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TaskStep in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchTaskStep() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        taskStep.setId(longCount.incrementAndGet());

        // Create the TaskStep
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTaskStepMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(taskStepDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TaskStep in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamTaskStep() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        taskStep.setId(longCount.incrementAndGet());

        // Create the TaskStep
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTaskStepMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(taskStepDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TaskStep in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateTaskStepWithPatch() throws Exception {
        // Initialize the database
        insertedTaskStep = taskStepRepository.saveAndFlush(taskStep);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the taskStep using partial update
        TaskStep partialUpdatedTaskStep = new TaskStep();
        partialUpdatedTaskStep.setId(taskStep.getId());

        partialUpdatedTaskStep
            .tenantId(UPDATED_TENANT_ID)
            .stepName(UPDATED_STEP_NAME)
            .stepDescription(UPDATED_STEP_DESCRIPTION)
            .outputData(UPDATED_OUTPUT_DATA)
            .executionTime(UPDATED_EXECUTION_TIME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restTaskStepMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTaskStep.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTaskStep))
            )
            .andExpect(status().isOk());

        // Validate the TaskStep in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTaskStepUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedTaskStep, taskStep), getPersistedTaskStep(taskStep));
    }

    @Test
    @Transactional
    void fullUpdateTaskStepWithPatch() throws Exception {
        // Initialize the database
        insertedTaskStep = taskStepRepository.saveAndFlush(taskStep);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the taskStep using partial update
        TaskStep partialUpdatedTaskStep = new TaskStep();
        partialUpdatedTaskStep.setId(taskStep.getId());

        partialUpdatedTaskStep
            .tenantId(UPDATED_TENANT_ID)
            .stepName(UPDATED_STEP_NAME)
            .stepDescription(UPDATED_STEP_DESCRIPTION)
            .status(UPDATED_STATUS.getValue())
            .stepOrder(UPDATED_STEP_ORDER)
            .inputData(UPDATED_INPUT_DATA)
            .outputData(UPDATED_OUTPUT_DATA)
            .errorMessage(UPDATED_ERROR_MESSAGE)
            .startTime(UPDATED_START_TIME)
            .endTime(UPDATED_END_TIME)
            .executionTime(UPDATED_EXECUTION_TIME)
            .retryCount(UPDATED_RETRY_COUNT)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restTaskStepMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTaskStep.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTaskStep))
            )
            .andExpect(status().isOk());

        // Validate the TaskStep in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTaskStepUpdatableFieldsEquals(partialUpdatedTaskStep, getPersistedTaskStep(partialUpdatedTaskStep));
    }

    @Test
    @Transactional
    void patchNonExistingTaskStep() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        taskStep.setId(longCount.incrementAndGet());

        // Create the TaskStep
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTaskStepMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, taskStepDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(taskStepDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TaskStep in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchTaskStep() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        taskStep.setId(longCount.incrementAndGet());

        // Create the TaskStep
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTaskStepMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(taskStepDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TaskStep in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamTaskStep() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        taskStep.setId(longCount.incrementAndGet());

        // Create the TaskStep
        TaskStepDTO taskStepDTO = taskStepMapper.toDto(taskStep);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTaskStepMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(taskStepDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TaskStep in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteTaskStep() throws Exception {
        // Initialize the database
        insertedTaskStep = taskStepRepository.saveAndFlush(taskStep);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the taskStep
        restTaskStepMockMvc
            .perform(delete(ENTITY_API_URL_ID, taskStep.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return taskStepRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected TaskStep getPersistedTaskStep(TaskStep taskStep) {
        return taskStepRepository.findById(taskStep.getId()).orElseThrow();
    }

    protected void assertPersistedTaskStepToMatchAllProperties(TaskStep expectedTaskStep) {
        assertTaskStepAllPropertiesEquals(expectedTaskStep, getPersistedTaskStep(expectedTaskStep));
    }

    protected void assertPersistedTaskStepToMatchUpdatableProperties(TaskStep expectedTaskStep) {
        assertTaskStepAllUpdatablePropertiesEquals(expectedTaskStep, getPersistedTaskStep(expectedTaskStep));
    }
}
