/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：TaskStepIntegrationTest.java
 * 包    名：com.whiskerguard.ai.service.agent
 * 描    述：TaskStep集成测试
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/27
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.TaskStep;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.TaskPriority;
import com.whiskerguard.ai.repository.AgentContextRepository;
import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.repository.TaskStepRepository;
import com.whiskerguard.ai.service.agent.dto.AgentTaskRequestDTO;
import com.whiskerguard.ai.service.agent.dto.AgentTaskResponseDTO;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * TaskStep集成测试
 * <p>
 * 验证Agent模块中TaskStep的完整集成，确保：
 * 1. createTask方法能正确创建AgentTask、AgentContext和TaskStep
 * 2. TaskStep与AgentTask正确关联
 * 3. 业务流程中TaskStep状态正确更新
 *
 * <AUTHOR>
 * @since 1.0
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class TaskStepIntegrationTest {

    @Autowired
    private ComplianceAgentService complianceAgentService;

    @Autowired
    private AgentTaskRepository agentTaskRepository;

    @Autowired
    private TaskStepRepository taskStepRepository;

    @Autowired
    private AgentContextRepository agentContextRepository;

    /**
     * 测试创建任务时是否正确创建TaskStep
     */
    @Test
    void testCreateTaskWithTaskSteps() throws Exception {
        // 1. 准备测试数据
        AgentTaskRequestDTO request = AgentTaskRequestDTO.builder()
            .tenantId(1L)
            .taskType(AgentTaskType.CONTRACT_REVIEW.name())
            .title("合同审查TaskStep集成测试")
            .description("测试TaskStep是否正确创建和关联")
            .priority(TaskPriority.NORMAL.name())
            .requestData("{\"contractId\":\"TEST_001\",\"contractType\":\"采购合同\",\"tenantId\":1,\"employeeId\":1001}")
            .build();

        // 2. 调用createTask方法
        AgentTaskResponseDTO response = complianceAgentService.createTask(request);

        // 3. 验证AgentTask创建成功
        assertThat(response).isNotNull();
        assertThat(response.getTaskId()).isNotNull();
        assertThat(response.getTaskType()).isEqualTo(AgentTaskType.CONTRACT_REVIEW);

        // 4. 等待异步任务执行（给一些时间让TaskStep创建）
        Thread.sleep(2000);

        // 5. 验证AgentTask存在
        AgentTask savedTask = agentTaskRepository.findById(response.getTaskId()).orElse(null);
        assertThat(savedTask).isNotNull();
        assertThat(savedTask.getTaskType()).isEqualTo("CONTRACT_REVIEW");

        // 6. 验证TaskStep被正确创建
        List<TaskStep> taskSteps = taskStepRepository.findByAgentTaskIdAndIsDeletedFalse(response.getTaskId());
        assertThat(taskSteps).isNotEmpty();
        assertThat(taskSteps.size()).isGreaterThan(0);

        // 7. 验证TaskStep与AgentTask的关联
        for (TaskStep step : taskSteps) {
            assertThat(step.getAgentTask()).isNotNull();
            assertThat(step.getAgentTask().getId()).isEqualTo(response.getTaskId());
            assertThat(step.getTenantId()).isEqualTo(1L);
            assertThat(step.getStepName()).isNotBlank();
            assertThat(step.getStepOrder()).isNotNull();
        }

        // 8. 验证AgentContext被正确创建
        Optional<AgentContext> contextOpt = agentContextRepository.findByAgentTaskId(response.getTaskId());
        assertThat(contextOpt).isPresent();

        AgentContext context = contextOpt.orElseThrow();
        assertThat(context.getAgentTaskId()).isEqualTo(response.getTaskId());
        assertThat(context.getContextType()).isEqualTo("CONTRACT_REVIEW");

        // 9. 打印调试信息
        System.out.println("=== TaskStep集成测试结果 ===");
        System.out.println("任务ID: " + response.getTaskId());
        System.out.println("创建的TaskStep数量: " + taskSteps.size());
        for (TaskStep step : taskSteps) {
            System.out.println(String.format("步骤 %d: %s (%s)", step.getStepOrder(), step.getStepName(), step.getStatus()));
        }
        System.out.println("上下文ID: " + context.getId());
        System.out.println("=========================");
    }

    /**
     * 测试外规内化任务的TaskStep创建
     */
    @Test
    void testRegulationInternalizationTaskSteps() throws Exception {
        // 1. 准备测试数据
        AgentTaskRequestDTO request = AgentTaskRequestDTO.builder()
            .tenantId(1L)
            .taskType(AgentTaskType.REGULATION_INTERNALIZATION.name())
            .title("外规内化TaskStep测试")
            .description("测试外规内化任务的TaskStep创建")
            .priority(TaskPriority.HIGH.name())
            .requestData("{\"regulationId\":\"REG_001\",\"companyId\":1,\"industryType\":\"电力\",\"tenantId\":1,\"employeeId\":1001}")
            .build();

        // 2. 调用createTask方法
        AgentTaskResponseDTO response = complianceAgentService.createTask(request);

        // 3. 等待异步任务执行
        Thread.sleep(2000);

        // 4. 验证TaskStep创建
        List<TaskStep> taskSteps = taskStepRepository.findByAgentTaskIdAndIsDeletedFalse(response.getTaskId());
        assertThat(taskSteps).isNotEmpty();

        // 5. 验证外规内化特定的步骤
        assertThat(taskSteps.stream().anyMatch(step -> step.getStepName().contains("法规内容检索"))).isTrue();
        assertThat(taskSteps.stream().anyMatch(step -> step.getStepName().contains("行业实践检索"))).isTrue();
        assertThat(taskSteps.stream().anyMatch(step -> step.getStepName().contains("内部制度生成"))).isTrue();

        // 6. 打印调试信息
        System.out.println("=== 外规内化TaskStep测试结果 ===");
        System.out.println("任务ID: " + response.getTaskId());
        System.out.println("创建的TaskStep数量: " + taskSteps.size());
        for (TaskStep step : taskSteps) {
            System.out.println(String.format("步骤 %d: %s", step.getStepOrder(), step.getStepName()));
        }
        System.out.println("==============================");
    }
}
