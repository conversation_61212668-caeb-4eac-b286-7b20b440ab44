package com.whiskerguard.ai.service.agent.core.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.domain.AgentTask;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.repository.AgentContextRepository;
import com.whiskerguard.ai.repository.AgentTaskRepository;
import com.whiskerguard.ai.security.SecurityUtils;
import com.whiskerguard.ai.service.agent.core.AgentContextCoreService;
import jakarta.persistence.EntityNotFoundException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Agent上下文核心服务实现
 * Agent Context Core Service Implementation
 *
 * 提供Agent任务执行过程中的上下文管理功能实现
 * Provide implementation of context management functions during Agent task execution
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
@Transactional
public class AgentContextCoreServiceImpl implements AgentContextCoreService {

    private static final Logger log = LoggerFactory.getLogger(AgentContextCoreServiceImpl.class);

    private final AgentContextRepository agentContextRepository;
    private final AgentTaskRepository agentTaskRepository;
    private final ObjectMapper objectMapper;

    /**
     * 构造函数注入依赖
     * Constructor dependency injection
     */
    public AgentContextCoreServiceImpl(
        AgentContextRepository agentContextRepository,
        AgentTaskRepository agentTaskRepository,
        ObjectMapper objectMapper
    ) {
        this.agentContextRepository = agentContextRepository;
        this.agentTaskRepository = agentTaskRepository;
        this.objectMapper = objectMapper;
    }

    @Override
    public AgentContext createContext(Long agentTaskId, String contextType) {
        log.debug(
            "创建Agent任务上下文 - 任务ID: {}, 类型: {} / Creating Agent task context - Task ID: {}, Type: {}",
            agentTaskId,
            contextType,
            agentTaskId,
            contextType
        );

        // 检查是否已存在上下文 / Check if context already exists
        Optional<AgentContext> existingContext = agentContextRepository.findByAgentTaskId(agentTaskId);
        if (existingContext.isPresent()) {
            log.warn("任务上下文已存在 - 任务ID: {} / Task context already exists - Task ID: {}", agentTaskId);
            return existingContext.orElseThrow();
        }

        // 查询AgentTask实体 / Query AgentTask entity
        AgentTask agentTask = agentTaskRepository.findById(agentTaskId)
            .orElseThrow(() -> new EntityNotFoundException("AgentTask不存在，ID: " + agentTaskId));

        // 创建新的上下文实体 / Create new context entity
        AgentContext context = new AgentContext();
        context.setAgentTaskId(agentTaskId);
        context.setAgentTask(agentTask); // 设置AgentTask关联
        context.setContextType(contextType);
        context.setContextData("{}");
        context.setVariables("{}");
        context.setTempFiles("{}");
        context.setHistory("[]");
        context.setVersion(1);
        context.setIsDeleted(false);

        // 设置审计字段 / Set audit fields
        String currentUser = SecurityUtils.getCurrentUserLogin().orElse("system");
        Instant now = Instant.now();
        context.setCreatedBy(currentUser);
        context.setCreatedAt(now);
        context.setUpdatedBy(currentUser);
        context.setUpdatedAt(now);

        // 保存并返回 / Save and return
        AgentContext savedContext = agentContextRepository.save(context);
        log.info(
            "成功创建Agent任务上下文 - ID: {}, 任务ID: {} / Successfully created Agent task context - ID: {}, Task ID: {}",
            savedContext.getId(),
            agentTaskId,
            savedContext.getId(),
            agentTaskId
        );

        return savedContext;
    }

    @Override
    @Transactional(readOnly = true)
    @Cacheable(value = "agent-context-by-task", key = "#agentTaskId")
    public AgentContext getContextByTaskId(Long agentTaskId) {
        log.debug("获取Agent任务上下文 - 任务ID: {} / Getting Agent task context - Task ID: {}", agentTaskId);

        return agentContextRepository.findByAgentTaskId(agentTaskId)
            .orElseThrow(() -> {
                log.error("未找到Agent任务上下文 - 任务ID: {} / Agent task context not found - Task ID: {}", agentTaskId);
                return new EntityNotFoundException("Agent上下文不存在，任务ID: " + agentTaskId);
            });
    }

    @Override
    public AgentContext getOrCreateContextByTaskId(Long agentTaskId) {
        log.debug("获取或创建Agent任务上下文 - 任务ID: {} / Get or create Agent task context - Task ID: {}", agentTaskId);

        Optional<AgentContext> existingContext = agentContextRepository.findByAgentTaskId(agentTaskId);
        if (existingContext.isPresent()) {
            log.debug("找到现有上下文 - 任务ID: {} / Found existing context - Task ID: {}", agentTaskId);
            return existingContext.orElseThrow();
        } else {
            log.debug("创建新上下文 - 任务ID: {} / Creating new context - Task ID: {}", agentTaskId);
            return createContext(agentTaskId, "default");
        }
    }

    @Override
    @CacheEvict(value = "agent-context-by-task", key = "#contextId")
    public void storeVariable(Long contextId, String key, Object value) {
        log.debug(
            "存储上下文变量 - 上下文ID: {}, 键: {} / Storing context variable - Context ID: {}, Key: {}",
            contextId,
            key,
            contextId,
            key
        );

        AgentContext context = getContextById(contextId);

        try {
            // 解析现有变量 / Parse existing variables
            Map<String, Object> variables = parseJsonToMap(context.getVariables());

            // 存储新变量 / Store new variable
            variables.put(key, value);

            // 更新变量JSON / Update variables JSON
            String updatedVariables = objectMapper.writeValueAsString(variables);
            context.setVariables(updatedVariables);

            // 记录历史 / Record history
            recordContextHistory(context, "STORE_VARIABLE", String.format("存储变量: %s / Stored variable: %s", key, key));

            // 更新审计字段并保存 / Update audit fields and save
            updateAuditFields(context);
            agentContextRepository.save(context);

            log.debug(
                "成功存储上下文变量 - 上下文ID: {}, 键: {} / Successfully stored context variable - Context ID: {}, Key: {}",
                contextId,
                key,
                contextId,
                key
            );
        } catch (JsonProcessingException e) {
            log.error("存储上下文变量失败 - JSON处理异常 / Failed to store context variable - JSON processing error", e);
            throw new RuntimeException("存储上下文变量失败 / Failed to store context variable", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public <T> T getVariable(Long contextId, String key, Class<T> valueType) {
        log.debug(
            "获取上下文变量 - 上下文ID: {}, 键: {} / Getting context variable - Context ID: {}, Key: {}",
            contextId,
            key,
            contextId,
            key
        );

        AgentContext context = getContextById(contextId);

        try {
            Map<String, Object> variables = parseJsonToMap(context.getVariables());
            Object value = variables.get(key);

            if (value == null) {
                return null;
            }

            // 类型转换 / Type conversion
            if (valueType.isInstance(value)) {
                return valueType.cast(value);
            } else {
                // 通过JSON转换处理复杂类型 / Handle complex types through JSON conversion
                String jsonValue = objectMapper.writeValueAsString(value);
                return objectMapper.readValue(jsonValue, valueType);
            }
        } catch (JsonProcessingException e) {
            log.error("获取上下文变量失败 - JSON处理异常 / Failed to get context variable - JSON processing error", e);
            throw new RuntimeException("获取上下文变量失败 / Failed to get context variable", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public boolean hasVariable(Long contextId, String key) {
        log.debug(
            "检查上下文变量是否存在 - 上下文ID: {}, 键: {} / Checking if context variable exists - Context ID: {}, Key: {}",
            contextId,
            key,
            contextId,
            key
        );

        AgentContext context = getContextById(contextId);
        Map<String, Object> variables = parseJsonToMap(context.getVariables());
        return variables.containsKey(key);
    }

    @Override
    @CacheEvict(value = "agent-context-by-task", key = "#contextId")
    public void removeVariable(Long contextId, String key) {
        log.debug(
            "移除上下文变量 - 上下文ID: {}, 键: {} / Removing context variable - Context ID: {}, Key: {}",
            contextId,
            key,
            contextId,
            key
        );

        AgentContext context = getContextById(contextId);

        try {
            Map<String, Object> variables = parseJsonToMap(context.getVariables());

            if (variables.remove(key) != null) {
                String updatedVariables = objectMapper.writeValueAsString(variables);
                context.setVariables(updatedVariables);

                recordContextHistory(context, "REMOVE_VARIABLE", String.format("移除变量: %s / Removed variable: %s", key, key));

                updateAuditFields(context);
                agentContextRepository.save(context);

                log.debug(
                    "成功移除上下文变量 - 上下文ID: {}, 键: {} / Successfully removed context variable - Context ID: {}, Key: {}",
                    contextId,
                    key,
                    contextId,
                    key
                );
            }
        } catch (JsonProcessingException e) {
            log.error("移除上下文变量失败 - JSON处理异常 / Failed to remove context variable - JSON processing error", e);
            throw new RuntimeException("移除上下文变量失败 / Failed to remove context variable", e);
        }
    }

    @Override
    @CacheEvict(value = "agent-context-by-task", key = "#contextId")
    public void addTempFile(Long contextId, String fileKey, String filePath) {
        log.debug(
            "添加临时文件引用 - 上下文ID: {}, 文件键: {}, 路径: {} / Adding temp file reference - Context ID: {}, File Key: {}, Path: {}",
            contextId,
            fileKey,
            filePath,
            contextId,
            fileKey,
            filePath
        );

        AgentContext context = getContextById(contextId);

        try {
            Map<String, Object> tempFiles = parseJsonToMap(context.getTempFiles());
            tempFiles.put(fileKey, filePath);

            String updatedTempFiles = objectMapper.writeValueAsString(tempFiles);
            context.setTempFiles(updatedTempFiles);

            recordContextHistory(
                context,
                "ADD_TEMP_FILE",
                String.format("添加临时文件: %s -> %s / Added temp file: %s -> %s", fileKey, filePath, fileKey, filePath)
            );

            updateAuditFields(context);
            agentContextRepository.save(context);
        } catch (JsonProcessingException e) {
            log.error("添加临时文件引用失败 - JSON处理异常 / Failed to add temp file reference - JSON processing error", e);
            throw new RuntimeException("添加临时文件引用失败 / Failed to add temp file reference", e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public String getTempFilePath(Long contextId, String fileKey) {
        log.debug(
            "获取临时文件路径 - 上下文ID: {}, 文件键: {} / Getting temp file path - Context ID: {}, File Key: {}",
            contextId,
            fileKey,
            contextId,
            fileKey
        );

        AgentContext context = getContextById(contextId);
        Map<String, Object> tempFiles = parseJsonToMap(context.getTempFiles());
        return (String) tempFiles.get(fileKey);
    }

    @Override
    @CacheEvict(value = "agent-context-by-task", key = "#contextId")
    public void removeTempFile(Long contextId, String fileKey) {
        log.debug(
            "移除临时文件引用 - 上下文ID: {}, 文件键: {} / Removing temp file reference - Context ID: {}, File Key: {}",
            contextId,
            fileKey,
            contextId,
            fileKey
        );

        AgentContext context = getContextById(contextId);

        try {
            Map<String, Object> tempFiles = parseJsonToMap(context.getTempFiles());

            if (tempFiles.remove(fileKey) != null) {
                String updatedTempFiles = objectMapper.writeValueAsString(tempFiles);
                context.setTempFiles(updatedTempFiles);

                recordContextHistory(
                    context,
                    "REMOVE_TEMP_FILE",
                    String.format("移除临时文件: %s / Removed temp file: %s", fileKey, fileKey)
                );

                updateAuditFields(context);
                agentContextRepository.save(context);
            }
        } catch (JsonProcessingException e) {
            log.error("移除临时文件引用失败 - JSON处理异常 / Failed to remove temp file reference - JSON processing error", e);
            throw new RuntimeException("移除临时文件引用失败 / Failed to remove temp file reference", e);
        }
    }

    @Override
    @CacheEvict(value = "agent-context-by-task", key = "#contextId")
    public void recordHistory(Long contextId, String action, String details) {
        log.debug(
            "记录上下文历史 - 上下文ID: {}, 动作: {} / Recording context history - Context ID: {}, Action: {}",
            contextId,
            action,
            contextId,
            action
        );

        AgentContext context = getContextById(contextId);
        recordContextHistory(context, action, details);
        updateAuditFields(context);
        agentContextRepository.save(context);
    }

    @Override
    @CacheEvict(value = "agent-context-by-task", key = "#contextId")
    public void updateContextData(Long contextId, String contextData) {
        log.debug("更新上下文数据 - 上下文ID: {} / Updating context data - Context ID: {}", contextId, contextId);

        AgentContext context = getContextById(contextId);
        context.setContextData(contextData);

        recordContextHistory(context, "UPDATE_CONTEXT_DATA", "更新上下文数据 / Updated context data");
        updateAuditFields(context);
        agentContextRepository.save(context);
    }

    @Override
    @Transactional(readOnly = true)
    public String getContextData(Long contextId) {
        log.debug("获取上下文数据 - 上下文ID: {} / Getting context data - Context ID: {}", contextId, contextId);

        AgentContext context = getContextById(contextId);
        return context.getContextData();
    }

    @Override
    public int cleanupExpiredContexts(AgentTaskType taskType) {
        log.info("清理过期上下文 - 任务类型: {} / Cleaning up expired contexts - Task Type: {}", taskType, taskType);

        // 这里可以实现基于创建时间或业务规则的清理逻辑
        // Here you can implement cleanup logic based on creation time or business rules
        // 例如：删除30天前的上下文
        // For example: delete contexts older than 30 days

        Instant cutoffTime = Instant.now().minusSeconds(30 * 24 * 60 * 60); // 30 days ago

        List<AgentContext> expiredContexts;
        if (taskType != null) {
            expiredContexts = agentContextRepository.findExpiredContextsByTaskType(cutoffTime, taskType.toString());
        } else {
            expiredContexts = agentContextRepository.findExpiredContexts(cutoffTime);
        }

        for (AgentContext context : expiredContexts) {
            context.setIsDeleted(true);
            updateAuditFields(context);
        }

        if (!expiredContexts.isEmpty()) {
            agentContextRepository.saveAll(expiredContexts);
        }

        int cleanedCount = expiredContexts.size();
        log.info("清理完成 - 清理数量: {} / Cleanup completed - Cleaned count: {}", cleanedCount, cleanedCount);

        return cleanedCount;
    }

    /**
     * 根据ID获取上下文实体
     * Get context entity by ID
     */
    private AgentContext getContextById(Long contextId) {
        return agentContextRepository.findById(contextId)
            .orElseThrow(() -> {
                log.error("未找到上下文 - ID: {} / Context not found - ID: {}", contextId, contextId);
                return new EntityNotFoundException("上下文不存在 - ID: " + contextId + " / Context not found - ID: " + contextId);
            });
    }

    /**
     * 解析JSON字符串为Map
     * Parse JSON string to Map
     */
    private Map<String, Object> parseJsonToMap(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new HashMap<>();
        }

        try {
            return objectMapper.readValue(json, new TypeReference<Map<String, Object>>() {});
        } catch (JsonProcessingException e) {
            log.warn("JSON解析失败，返回空Map - JSON: {} / JSON parsing failed, returning empty Map - JSON: {}", json, json);
            return new HashMap<>();
        }
    }

    /**
     * 记录上下文历史
     * Record context history
     */
    private void recordContextHistory(AgentContext context, String action, String details) {
        try {
            List<Map<String, Object>> history = parseJsonToList(context.getHistory());

            Map<String, Object> historyEntry = new HashMap<>();
            historyEntry.put("timestamp", Instant.now().toString());
            historyEntry.put("action", action);
            historyEntry.put("details", details);
            historyEntry.put("user", SecurityUtils.getCurrentUserLogin().orElse("system"));

            history.add(historyEntry);

            // 限制历史记录数量，保留最近100条 / Limit history records, keep latest 100
            if (history.size() > 100) {
                history = history.subList(history.size() - 100, history.size());
            }

            String updatedHistory = objectMapper.writeValueAsString(history);
            context.setHistory(updatedHistory);
        } catch (JsonProcessingException e) {
            log.error("记录上下文历史失败 - JSON处理异常 / Failed to record context history - JSON processing error", e);
        }
    }

    /**
     * 解析JSON字符串为List
     * Parse JSON string to List
     */
    private List<Map<String, Object>> parseJsonToList(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new ArrayList<>();
        }

        try {
            return objectMapper.readValue(json, new TypeReference<List<Map<String, Object>>>() {});
        } catch (JsonProcessingException e) {
            log.warn("JSON解析失败，返回空List - JSON: {} / JSON parsing failed, returning empty List - JSON: {}", json, json);
            return new ArrayList<>();
        }
    }

    /**
     * 更新审计字段
     * Update audit fields
     */
    private void updateAuditFields(AgentContext context) {
        String currentUser = SecurityUtils.getCurrentUserLogin().orElse("system");
        context.setUpdatedBy(currentUser);
        context.setUpdatedAt(Instant.now());
        context.setVersion(context.getVersion() + 1);
    }
}
