/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：PolicyReviewAgentService.java
 * 包    名：com.whiskerguard.ai.service.agent.business
 * 描    述：政策审查智能体服务
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.service.agent.business;

import com.whiskerguard.ai.client.RetrievalServiceClient;
import com.whiskerguard.ai.domain.AgentContext;
import com.whiskerguard.ai.domain.enumeration.AgentTaskType;
import com.whiskerguard.ai.domain.enumeration.RiskLevel;
import com.whiskerguard.ai.service.agent.core.KnowledgeRetrievalService;
import com.whiskerguard.ai.service.agent.core.LlmOrchestrationService;
import com.whiskerguard.ai.service.agent.core.model.StepDefinition;
import com.whiskerguard.ai.service.agent.core.workflow.WorkflowDefinitionFactory;
import com.whiskerguard.ai.service.dto.AiInvocationRequestDTO;
import com.whiskerguard.ai.service.dto.PolicyReviewRequestDTO;
import com.whiskerguard.ai.service.dto.PolicyReviewResponseDTO;
import com.whiskerguard.ai.service.invocation.AiInvocationService;
import com.whiskerguard.ai.service.invocation.ModelEnsembleService;
import com.whiskerguard.ai.service.invocation.RagHelper;
import java.time.Instant;
import java.util.UUID;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 政策审查智能体服务
 * <p>
 * 负责对企业内部政策文档进行智能化审查分析。
 * 通过AI技术对政策文档进行合规性检查、逻辑分析、内容优化建议等。
 *
 * 主要功能：
 * 1. 政策文档解析
 * 2. 合规性检查
 * 3. 逻辑完整性分析
 * 4. 优化建议生成
 *
 * <AUTHOR>
 * @since 1.0
 */
@Service
public class PolicyReviewAgentService extends AbstractBusinessAgentService {

    private static final Logger log = LoggerFactory.getLogger(PolicyReviewAgentService.class);

    private final KnowledgeRetrievalService knowledgeRetrievalService;
    private final LlmOrchestrationService llmOrchestrationService;
    private final AiInvocationService aiInvocationService;
    private final RetrievalServiceClient retrievalServiceClient;
    private final RagHelper ragHelper;
    private final ModelEnsembleService modelEnsembleService;

    /**
     * 默认使用的工具Key
     */
    private static final String DEFAULT_TOOL_KEY = "kimi";

    /**
     * 构造函数注入依赖
     */
    @Autowired
    public PolicyReviewAgentService(
        KnowledgeRetrievalService knowledgeRetrievalService,
        LlmOrchestrationService llmOrchestrationService,
        AiInvocationService aiInvocationService,
        RetrievalServiceClient retrievalServiceClient,
        RagHelper ragHelper,
        ModelEnsembleService modelEnsembleService
    ) {
        this.knowledgeRetrievalService = knowledgeRetrievalService;
        this.llmOrchestrationService = llmOrchestrationService;
        this.aiInvocationService = aiInvocationService;
        this.retrievalServiceClient = retrievalServiceClient;
        this.ragHelper = ragHelper;
        this.modelEnsembleService = modelEnsembleService;
    }

    /**
     * 处理政策审查请求 - 带AgentTask ID
     * Processing Policy Review Request with AgentTask ID
     *
     * 采用标准化的Agent处理流程：
     * 1. 创建上下文与任务
     * 2. 执行标准工作流
     * 3. 记录处理过程
     * 4. 返回标准化响应
     *
     * @param request 政策审查请求 / Policy review request
     * @param agentTaskId Agent任务ID / Agent task ID
     * @return 政策审查响应 / Policy review response
     */
    public PolicyReviewResponseDTO processPolicyReview(PolicyReviewRequestDTO request, Long agentTaskId) {
        log.info("开始处理政策审查请求，政策ID: {}, 政策类型: {}, 任务ID: {}", request.getPolicyId(), request.getPolicyType(), agentTaskId);

        AgentContext context = null;
        try {
            // 1. 创建业务上下文 / Create business context
            Map<String, Object> businessData = new HashMap<>();
            businessData.put("policyId", request.getPolicyId());
            businessData.put("policyType", request.getPolicyType());
            businessData.put("request", request);

            context = createBusinessContext(
                request.getTenantId() != null ? request.getTenantId().toString() : null,
                agentTaskId,
                AgentTaskType.POLICY_REVIEW,
                businessData
            );

            // 2. 更新处理进度 / Update processing progress
            updateBusinessProgress(context, 10, "INITIALIZING", Map.of("phase", "context_creation", "policyId", request.getPolicyId()));

            // 3. 执行核心业务逻辑 / Execute core business logic
            Map<String, Object> processResult = processPolicyReviewCore(request, context);

            // 4. 更新完成进度 / Update completion progress
            updateBusinessProgress(context, 100, "COMPLETED", Map.of("phase", "completion", "result", "success"));

            // 5. 完成业务处理 / Complete business processing
            completeBusinessProcessing(context, processResult);

            // 6. 构建响应 / Build response
            return buildPolicyReviewResponse(request, processResult);

        } catch (Exception e) {
            log.error("政策审查处理失败", e);
            if (context != null) {
                handleBusinessException(context, e, "POLICY_REVIEW_PROCESSING");
            }
            throw new RuntimeException("政策审查处理失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理政策审查请求 - 兼容性方法
     * Process Policy Review Request - Compatibility method
     *
     * @param request 政策审查请求 / Policy review request
     * @return 政策审查响应 / Policy review response
     */
    public PolicyReviewResponseDTO processPolicyReview(PolicyReviewRequestDTO request) {
        log.info("开始处理政策审查请求，政策ID: {}, 政策类型: {}", request.getPolicyId(), request.getPolicyType());

        // 生成临时任务ID用于上下文创建
        Long tempTaskId = System.currentTimeMillis();
        return processPolicyReview(request, tempTaskId);
    }

    /**
     * 核心政策审查处理逻辑
     */
    private Map<String, Object> processPolicyReviewCore(PolicyReviewRequestDTO request, AgentContext context) {
        log.debug("执行核心政策审查逻辑，政策ID: {}", request.getPolicyId());

        // 更新处理进度
        updateBusinessProgress(context, 30, "PROCESSING", Map.of("phase", "core_analysis", "policyId", request.getPolicyId()));

        // 模拟政策审查处理
        Map<String, Object> result = new HashMap<>();
        result.put("policyId", request.getPolicyId());
        result.put("policyType", request.getPolicyType());
        result.put("riskLevel", "MEDIUM");
        result.put("riskScore", 60);
        result.put("summary", "政策审查完成，发现中等风险");
        result.put("recommendations", List.of("建议1：完善条款", "建议2：加强监管"));
        result.put("processTime", Instant.now());

        // 更新处理进度
        updateBusinessProgress(context, 80, "ANALYZING", Map.of("phase", "result_generation", "result", "success"));

        return result;
    }



    /**
     * 构建政策审查响应
     */
    private PolicyReviewResponseDTO buildPolicyReviewResponse(PolicyReviewRequestDTO request, Map<String, Object> processResult) {
        PolicyReviewResponseDTO response = new PolicyReviewResponseDTO();

        // 设置基本信息
        response.setReviewId(generateReviewId());
        response.setOverallRiskLevel(RiskLevel.valueOf((String) processResult.getOrDefault("riskLevel", "MEDIUM")));
        response.setRiskScore((Integer) processResult.getOrDefault("riskScore", 60));
        response.setRiskSummary((String) processResult.getOrDefault("summary", "政策审查完成"));
        response.setReviewTime(Instant.now());
        response.setReviewStatus("COMPLETED");
        response.setReviewDuration(3000L);

        // 设置建议
        @SuppressWarnings("unchecked")
        List<String> recommendations = (List<String>) processResult.getOrDefault("recommendations", List.of("无特殊建议"));
        response.setRecommendations(recommendations);

        return response;
    }

    /**
     * 生成审查ID
     */
    private Long generateReviewId() {
        return Math.abs(UUID.randomUUID().getLeastSignificantBits()) % 10000000000L;
    }

    /**
     * 检索政策内容
     */
    private String retrievePolicyContent(PolicyReviewRequestDTO request) {
        log.debug("检索政策内容，政策ID: {}", request.getPolicyId());

        try {
            // 构建检索参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("policyId", request.getPolicyId());
            queryParams.put("contentType", "policy");

            // 调用知识检索服务
            String retrievalResult = knowledgeRetrievalService.retrieveKnowledge(
                request.getTenantId(),
                "policy_retrieval",
                "policy_content",
                queryParams
            );

            return extractContentFromResult(retrievalResult, "政策内容");
        } catch (Exception e) {
            log.error("检索政策内容失败", e);
            return "政策内容检索失败：" + e.getMessage();
        }
    }

    /**
     * 检索相关法规
     */
    private String retrieveRelatedRegulations(PolicyReviewRequestDTO request) {
        log.debug("检索相关法规");

        try {
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("domain", request.getPolicyType());
            queryParams.put("type", "regulation");

            String retrievalResult = knowledgeRetrievalService.retrieveKnowledge(
                request.getTenantId(),
                "regulation_retrieval",
                "related_regulations",
                queryParams
            );

            return extractContentFromResult(retrievalResult, "相关法规");
        } catch (Exception e) {
            log.error("检索相关法规失败", e);
            return "相关法规检索失败：" + e.getMessage();
        }
    }

    /**
     * 检索行业标准
     */
    private String retrieveIndustryStandards(PolicyReviewRequestDTO request) {
        log.debug("检索行业标准");

        try {
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("industry", request.getIndustryType());
            queryParams.put("type", "standard");

            String retrievalResult = knowledgeRetrievalService.retrieveKnowledge(
                request.getTenantId(),
                "standard_retrieval",
                "industry_standards",
                queryParams
            );

            return extractContentFromResult(retrievalResult, "行业标准");
        } catch (Exception e) {
            log.error("检索行业标准失败", e);
            return "行业标准检索失败：" + e.getMessage();
        }
    }

    /**
     * 构建政策审查提示词
     */
    private String buildPolicyReviewPrompt(
        PolicyReviewRequestDTO request,
        String policyContent,
        String relatedRegulations,
        String industryStandards
    ) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请对以下企业政策进行全面审查分析：\n\n");

        prompt.append("【政策内容】\n").append(policyContent).append("\n\n");
        prompt.append("【相关法规】\n").append(relatedRegulations).append("\n\n");
        prompt.append("【行业标准】\n").append(industryStandards).append("\n\n");
        prompt.append("【审查类型】\n").append(request.getPolicyType()).append("\n\n");

        prompt.append("请从以下维度进行审查：\n");
        prompt.append("1. 合规性检查 - 是否符合相关法规要求\n");
        prompt.append("2. 完整性分析 - 政策条款是否完整覆盖\n");
        prompt.append("3. 逻辑一致性 - 政策内容是否逻辑清晰\n");
        prompt.append("4. 可操作性 - 政策是否具有实际可执行性\n");
        prompt.append("5. 风险识别 - 识别潜在的合规风险点\n");

        return prompt.toString();
    }

    /**
     * 执行AI审查
     */
    private String executeAiReview(PolicyReviewRequestDTO request, String reviewPrompt) {
        log.debug("执行AI审查");

        try {
            // 创建AI调用请求
            AiInvocationRequestDTO aiRequest = new AiInvocationRequestDTO(
                DEFAULT_TOOL_KEY,
                reviewPrompt,
                null,
                request.getTenantId(),
                request.getEmployeeId()
            );

            // 确定使用的模型
            List<String> modelsList = request.getModelNames() != null && !request.getModelNames().isEmpty()
                ? request.getModelNames()
                : Arrays.asList("doubao", "kimi");

            // 使用多模型集成
            return modelEnsembleService.generateEnsembleResponse(aiRequest, modelsList);
        } catch (Exception e) {
            log.error("执行AI审查失败", e);
            return "AI审查执行失败：" + e.getMessage();
        }
    }

    /**
     * 生成优化建议
     */
    private String generateOptimizationSuggestions(PolicyReviewRequestDTO request, String reviewResult) {
        log.debug("生成优化建议");

        StringBuilder suggestions = new StringBuilder();
        suggestions.append("基于审查结果的优化建议：\n\n");

        // 根据审查结果生成针对性建议
        if (reviewResult.contains("合规性") || reviewResult.contains("法规")) {
            suggestions.append("1. 合规性优化建议\n");
            suggestions.append("   - 建议对标最新法规要求进行政策更新\n");
            suggestions.append("   - 增强合规性条款的具体化描述\n\n");
        }

        if (reviewResult.contains("风险") || reviewResult.contains("问题")) {
            suggestions.append("2. 风险控制建议\n");
            suggestions.append("   - 针对识别的风险点制定专项控制措施\n");
            suggestions.append("   - 建立风险监控和预警机制\n\n");
        }

        suggestions.append("3. 通用优化建议\n");
        suggestions.append("   - 定期审查和更新政策内容\n");
        suggestions.append("   - 加强员工政策培训和宣贯\n");
        suggestions.append("   - 建立政策执行效果评估机制\n");

        return suggestions.toString();
    }

    /**
     * 计算合规分数
     */
    private Integer calculateComplianceScore(String reviewResult) {
        // 简单的评分逻辑，实际应该更复杂
        if (reviewResult.contains("完全符合") || reviewResult.contains("高度合规")) {
            return 95;
        } else if (reviewResult.contains("基本符合") || reviewResult.contains("合规")) {
            return 80;
        } else if (reviewResult.contains("部分符合")) {
            return 65;
        } else {
            return 50;
        }
    }

    /**
     * 从检索结果中提取内容
     */
    private String extractContentFromResult(String result, String defaultContent) {
        if (result != null && !result.trim().isEmpty()) {
            return result;
        }
        return defaultContent + "暂无数据";
    }

    /**
     * 获取自定义工作流定义
     * Custom Workflow Definition for Policy Review
     */
    @Override
    protected List<StepDefinition> getCustomWorkflowDefinition(AgentTaskType taskType) {
        // 返回政策审查的自定义工作流定义
        return WorkflowDefinitionFactory.getPolicyReviewWorkflow();
    }
}
